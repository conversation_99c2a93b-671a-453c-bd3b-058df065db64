"use client";

import React, { useRef, useEffect, useState, useCallback } from "react";
import { useSidebar } from "@/components/ui/sidebar";
import { useMapLanguage } from "@/hooks/useMapLanguage";
import type { MapContainerProps, DaycareFeature } from "@/types/daycare";
import { LoadingOverlay } from "./components/LoadingOverlay";
import { MapControls } from "./components/MapControls";
import { MapPopup, MapPopupStyles } from "./components/MapPopup";
import { EventHandler } from "./core/EventHandler";
import { LayerManager } from "./core/LayerManager";
import { MapManager } from "./core/MapManager";
import { useMapData } from "./hooks/useMapData";
import { useMapState } from "./hooks/useMapState";
import { quickPerformanceCheck } from "./utils/performance-monitor";

import "mapbox-gl/dist/mapbox-gl.css";
import "./styles/map-theme.css";
import "./utils/theme-debug";

/**
 * 地图容器组件 - 负责Mapbox地图的初始化和数据展示
 * 支持主题适配和响应式设计
 */
const MapContainer: React.FC<MapContainerProps> = ({
  className = "",
  height = "100%",
  onMapLoad,
  onError,
}) => {
  const { t } = useMapLanguage();
  const { state: sidebarState } = useSidebar();
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapManagerRef = useRef<MapManager | null>(null);
  const layerManagerRef = useRef<LayerManager | null>(null);
  const eventHandlerRef = useRef<EventHandler | null>(null);

  // 使用自定义hooks管理状态和数据
  const mapState = useMapState();
  const mapData = useMapData();
  const [currentPopup, setCurrentPopup] = useState<{
    popup: any;
    feature: DaycareFeature;
  } | null>(null);

  // 解构出需要的方法，避免在useEffect依赖中使用整个对象
  const { updateLoadingStatus, setMapLoaded, setLoadingState, setErrorState } =
    mapState;
  const { preloadZipBoundaries, loadDaycareData } = mapData;

  // 加载托儿所数据并添加到地图
  const loadDaycareDataAndAddToMap = useCallback(
    async (
      zipData: any,
      layerManager: LayerManager,
      eventHandler: EventHandler
    ): Promise<void> => {
      try {
        const result = await loadDaycareData(zipData);

        if (result.type === "zip-areas") {
          // 添加ZIP区域图层
          layerManager.addZipAreasToMap(result.data);
          eventHandler.initializeZipAreaEvents();
        } else if (result.type === "points") {
          // 添加点图层
          layerManager.addDataToMap(result.data);
          eventHandler.initializeCircleEvents();
        }
      } catch (error) {
        console.error("Error loading and adding daycare data:", error);
        setErrorState(error as Error);
        onError?.(error as Error);
      }
    },
    [loadDaycareData, setErrorState, onError]
  );

  // 初始化地图
  useEffect(() => {
    if (!mapContainerRef.current) {
      return;
    }

    // 设置初始加载状态
    updateLoadingStatus(t("map:loading.initializing"));

    try {
      // 创建地图管理器
      const mapManager = new MapManager();
      mapManagerRef.current = mapManager;

      // 创建地图实例
      mapManager.createMap(mapContainerRef.current);

      // 创建图层管理器
      const layerManager = new LayerManager(mapManager);
      layerManagerRef.current = layerManager;

      // 创建事件处理器
      const eventHandler = new EventHandler(mapManager);
      eventHandlerRef.current = eventHandler;

      // 设置弹窗创建回调
      eventHandler.setPopupCreateCallback(
        (popup: any, feature: DaycareFeature) => {
          setCurrentPopup({ popup, feature });
        }
      );

      // 地图加载完成事件
      mapManager.onLoad(async () => {
        setMapLoaded(true);
        updateLoadingStatus(t("map:loading.loadingBoundaries"));

        // 先预加载ZIP边界数据，然后加载托儿所数据
        const zipData = await preloadZipBoundaries();

        updateLoadingStatus(t("map:loading.loadingData"));
        await loadDaycareDataAndAddToMap(zipData, layerManager, eventHandler);

        updateLoadingStatus(t("map:loading.complete"));
        setLoadingState(false);
        onMapLoad?.();

        // 监听主题变化事件 - 优化版本
        const mapInstance = mapManager.getMap();
        if (mapInstance) {
          mapInstance.on("themeChanged", async () => {
            const performanceCheck = quickPerformanceCheck.themeSwitch();

            try {
              const currentMap = mapManager.getMap();
              if (!currentMap) {
                return;
              }

              // 快速检查地图状态，减少等待时间
              if (!currentMap.isStyleLoaded()) {
                await new Promise((resolve) => {
                  const checkStyle = () => {
                    if (currentMap.isStyleLoaded()) {
                      resolve(void 0);
                    } else {
                      setTimeout(checkStyle, 10); // 进一步减少轮询间隔
                    }
                  };
                  checkStyle();
                });
              }

              // 优化：使用快速重新应用方法
              const fastReapplied = layerManager.fastReapplyLayers();

              if (fastReapplied) {
                // 快速重新应用成功，重新初始化事件
                if (currentMap.getSource("daycare-zip-areas")) {
                  eventHandler.initializeZipAreaEvents();
                } else if (currentMap.getSource("daycare-data-2023")) {
                  eventHandler.initializeCircleEvents();
                }
              } else {
                // 快速重新应用失败，回退到完整重新加载
                await loadDaycareDataAndAddToMap(
                  zipData,
                  layerManager,
                  eventHandler
                );
              }

              // 如果之前显示了ZIP边界，重新显示
              if (currentMap.getLayer("zip-boundaries-fill")) {
                layerManager.addZipBoundariesToMap(zipData);
              }

              // 记录性能结果
              const result = performanceCheck.end();
              if (result.duration > 300) {
                console.warn(
                  `⚠️ 主题切换性能较慢: ${result.duration.toFixed(2)}ms`
                );
              }
            } catch (error) {
              console.error("主题切换时重新加载数据失败:", error);
              performanceCheck.end();
            }
          });
        }
      });

      // 错误处理
      mapManager.onError((e) => {
        console.error("Map load error:", e);
        setErrorState(new Error("地图加载失败"));
        onError?.(new Error("地图加载失败"));
      });

      // 初始化飞行到位置事件监听
      const cleanupFlyTo = eventHandler.initializeFlyToEvents();

      // 清理函数
      return () => {
        cleanupFlyTo?.();
        eventHandler.cleanup();
        mapManager.destroyMap();
        mapManagerRef.current = null;
        layerManagerRef.current = null;
        eventHandlerRef.current = null;
      };
    } catch (error) {
      updateLoadingStatus("地图加载失败");
      setLoadingState(false);
      setErrorState(error as Error);
      onError?.(error as Error);
    }
  }, [
    onMapLoad,
    onError,
    t,
    updateLoadingStatus,
    setMapLoaded,
    setLoadingState,
    setErrorState,
    preloadZipBoundaries,
    loadDaycareDataAndAddToMap,
  ]);

  // 监听侧边栏状态变化，触发地图resize
  useEffect(() => {
    const mapManager = mapManagerRef.current;
    if (!mapManager) {
      return;
    }

    // 使用setTimeout确保DOM更新完成后再resize
    const timeoutId = setTimeout(() => {
      mapManager.resize();
    }, 300); // 300ms延迟，匹配sidebar的transition duration

    return () => {
      clearTimeout(timeoutId);
    };
  }, [sidebarState]);

  // ZIP边界控制逻辑
  const handleToggleZipBoundaries = async () => {
    const layerManager = layerManagerRef.current;
    if (!layerManager) {
      return;
    }

    mapState.toggleZipBoundaries();

    if (!mapState.showZipBoundaries) {
      // 显示ZIP边界
      mapState.setBoundariesRendering(true);

      let zipData = mapData.zipBoundariesData;
      if (!zipData) {
        zipData = await mapData.preloadZipBoundaries();
      }

      if (zipData) {
        await layerManager.addZipBoundariesToMap(zipData);
      }

      mapState.setBoundariesRendering(false);
    } else {
      // 隐藏ZIP边界
      layerManager.hideZipBoundaries();
    }
  };

  return (
    <>
      <div className="relative" style={{ height }}>
        <div
          ref={mapContainerRef}
          className={`map-container ${className}`}
          style={{ height: "100%", width: "100%" }}
        />

        {/* 加载状态覆盖层 */}
        <LoadingOverlay
          isLoading={mapState.isLoading}
          loadingStatus={mapState.loadingStatus}
        />

        {/* 地图控制按钮 */}
        <MapControls
          showZipBoundaries={mapState.showZipBoundaries}
          isZipDataLoaded={mapData.isZipDataLoaded}
          isShowingBoundaries={mapState.isShowingBoundaries}
          onToggleZipBoundaries={handleToggleZipBoundaries}
        />

        {/* 弹窗组件 */}
        {currentPopup && (
          <MapPopup popup={currentPopup.popup} feature={currentPopup.feature} />
        )}
      </div>

      {/* 全局弹窗样式 */}
      <MapPopupStyles />
    </>
  );
};

export default MapContainer;
