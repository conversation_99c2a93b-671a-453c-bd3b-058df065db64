"use client";

import React, { useState, useEffect } from "react";
import { LocalDataLoader, localDataManager } from "../data/local-data-manager";

/**
 * 本地数据管理器测试组件
 * 用于验证本地数据加载是否正常工作
 */
export const LocalDataTest: React.FC = () => {
  const [testResults, setTestResults] = useState<any>({
    initialized: false,
    zipBoundaries: null,
    daycareGeoJson: null,
    daycareProperties: null,
    daycareStats: null,
    errors: [],
    loading: true,
  });

  const [localDataLoader] = useState(() => new LocalDataLoader());

  useEffect(() => {
    const runTests = async () => {
      const results = {
        initialized: false,
        zipBoundaries: null,
        daycareGeoJson: null,
        daycareProperties: null,
        daycareStats: null,
        errors: [] as string[],
        loading: false,
      };

      try {
        console.log("🔄 开始测试本地数据管理器...");

        // 测试初始化
        await localDataManager.initialize();
        results.initialized = localDataManager.isReady();
        console.log("✅ 初始化完成:", results.initialized);

        // 测试ZIP边界数据
        try {
          const zipData = await localDataLoader.preloadZipBoundaries();
          results.zipBoundaries = {
            loaded: !!zipData,
            featureCount: zipData?.features?.length || 0,
            sampleFeature: zipData?.features?.[0]?.properties || null,
          };
          console.log("✅ ZIP边界数据测试完成:", results.zipBoundaries);
        } catch (error) {
          const errorMsg = `ZIP边界数据加载失败: ${error}`;
          results.errors.push(errorMsg);
          console.error("❌", errorMsg);
        }

        // 测试托儿所GeoJSON数据
        try {
          const pointData = await localDataLoader.loadPointData();
          results.daycareGeoJson = {
            loaded: !!pointData,
            type: pointData.type,
            featureCount: pointData.data?.features?.length || 0,
            sampleFeature: pointData.data?.features?.[0]?.properties || null,
          };
          console.log("✅ 托儿所GeoJSON数据测试完成:", results.daycareGeoJson);
        } catch (error) {
          const errorMsg = `托儿所GeoJSON数据加载失败: ${error}`;
          results.errors.push(errorMsg);
          console.error("❌", errorMsg);
        }

        // 测试托儿所属性数据
        try {
          const properties = localDataManager.getDaycareProperties();
          results.daycareProperties = {
            loaded: !!properties,
            zipCount: properties ? Object.keys(properties).length : 0,
            sampleZip: properties ? Object.keys(properties)[0] : null,
            sampleData: properties ? properties[Object.keys(properties)[0]] : null,
          };
          console.log("✅ 托儿所属性数据测试完成:", results.daycareProperties);
        } catch (error) {
          const errorMsg = `托儿所属性数据加载失败: ${error}`;
          results.errors.push(errorMsg);
          console.error("❌", errorMsg);
        }

        // 测试统计数据
        try {
          const stats = await localDataLoader.getStats();
          results.daycareStats = {
            loaded: !!stats,
            totalDaycares: stats?.total_daycares || 0,
            totalCapacity: stats?.total_capacity || 0,
            overallSaturation: stats?.overall_saturation || 0,
          };
          console.log("✅ 统计数据测试完成:", results.daycareStats);
        } catch (error) {
          const errorMsg = `统计数据加载失败: ${error}`;
          results.errors.push(errorMsg);
          console.error("❌", errorMsg);
        }

        console.log("🎉 本地数据管理器测试完成");
      } catch (error) {
        const errorMsg = `测试过程中发生错误: ${error}`;
        results.errors.push(errorMsg);
        console.error("❌", errorMsg);
      }

      setTestResults(results);
    };

    runTests();
  }, [localDataLoader]);

  const getStatusIcon = (loaded: boolean) => {
    return loaded ? "✅" : "❌";
  };

  const getStatusColor = (loaded: boolean) => {
    return loaded ? "text-green-600" : "text-red-600";
  };

  if (testResults.loading) {
    return (
      <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <h2 className="text-xl font-bold mb-4">本地数据管理器测试</h2>
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span>正在测试本地数据加载...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
        本地数据管理器测试结果
      </h2>

      {/* 总体状态 */}
      <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <h3 className="font-semibold mb-2 text-gray-900 dark:text-white">总体状态</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className={getStatusColor(testResults.initialized)}>
              {getStatusIcon(testResults.initialized)} 初始化状态
            </span>
          </div>
          <div>
            <span className={getStatusColor(testResults.errors.length === 0)}>
              {getStatusIcon(testResults.errors.length === 0)} 错误数量: {testResults.errors.length}
            </span>
          </div>
        </div>
      </div>

      {/* ZIP边界数据 */}
      <div className="mb-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
        <h3 className="font-semibold mb-2 text-gray-900 dark:text-white">
          {getStatusIcon(testResults.zipBoundaries?.loaded)} ZIP边界数据
        </h3>
        {testResults.zipBoundaries && (
          <div className="text-sm text-gray-600 dark:text-gray-300">
            <p>特征数量: {testResults.zipBoundaries.featureCount}</p>
            {testResults.zipBoundaries.sampleFeature && (
              <p>示例ZIP: {testResults.zipBoundaries.sampleFeature.ZCTA5CE10 || testResults.zipBoundaries.sampleFeature.zipCode}</p>
            )}
          </div>
        )}
      </div>

      {/* 托儿所GeoJSON数据 */}
      <div className="mb-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
        <h3 className="font-semibold mb-2 text-gray-900 dark:text-white">
          {getStatusIcon(testResults.daycareGeoJson?.loaded)} 托儿所GeoJSON数据
        </h3>
        {testResults.daycareGeoJson && (
          <div className="text-sm text-gray-600 dark:text-gray-300">
            <p>数据类型: {testResults.daycareGeoJson.type}</p>
            <p>特征数量: {testResults.daycareGeoJson.featureCount}</p>
            {testResults.daycareGeoJson.sampleFeature && (
              <p>示例托儿所: {testResults.daycareGeoJson.sampleFeature.name || "未知"}</p>
            )}
          </div>
        )}
      </div>

      {/* 托儿所属性数据 */}
      <div className="mb-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
        <h3 className="font-semibold mb-2 text-gray-900 dark:text-white">
          {getStatusIcon(testResults.daycareProperties?.loaded)} 托儿所属性数据
        </h3>
        {testResults.daycareProperties && (
          <div className="text-sm text-gray-600 dark:text-gray-300">
            <p>ZIP区域数量: {testResults.daycareProperties.zipCount}</p>
            {testResults.daycareProperties.sampleZip && (
              <p>示例ZIP: {testResults.daycareProperties.sampleZip}</p>
            )}
            {testResults.daycareProperties.sampleData && (
              <p>示例饱和度: {(testResults.daycareProperties.sampleData.saturation * 100).toFixed(1)}%</p>
            )}
          </div>
        )}
      </div>

      {/* 统计数据 */}
      <div className="mb-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
        <h3 className="font-semibold mb-2 text-gray-900 dark:text-white">
          {getStatusIcon(testResults.daycareStats?.loaded)} 统计数据
        </h3>
        {testResults.daycareStats && (
          <div className="text-sm text-gray-600 dark:text-gray-300">
            <p>总托儿所数量: {testResults.daycareStats.totalDaycares}</p>
            <p>总容量: {testResults.daycareStats.totalCapacity}</p>
            <p>整体饱和度: {(testResults.daycareStats.overallSaturation * 100).toFixed(1)}%</p>
          </div>
        )}
      </div>

      {/* 错误信息 */}
      {testResults.errors.length > 0 && (
        <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <h3 className="font-semibold mb-2 text-red-800 dark:text-red-200">错误信息</h3>
          <ul className="text-sm text-red-600 dark:text-red-300">
            {testResults.errors.map((error, index) => (
              <li key={index} className="mb-1">• {error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <h3 className="font-semibold mb-2 text-blue-800 dark:text-blue-200">使用说明</h3>
        <div className="text-sm text-blue-600 dark:text-blue-300">
          <p>• 本地数据管理器已替代API请求，直接从本地文件加载数据</p>
          <p>• 数据文件位置: /data/processed_2023/ 和 /public/data/</p>
          <p>• 如果看到错误，请确保数据文件存在且格式正确</p>
          <p>• 在开发环境中，可以通过 window.localDataManager 访问数据管理器</p>
        </div>
      </div>
    </div>
  );
};

export default LocalDataTest;
